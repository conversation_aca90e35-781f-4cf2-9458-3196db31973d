<template>
  <q-toolbar class="bg-color q-pt-md">
    <div class="row full-width no-wrap items-center">
      <!-- ใช้ q-space เพื่อดันปุ่มที่เหลือไปทางขวา -->
      <q-space />

      <!-- ปุ่มเมนู -->
      <div class="col-auto row no-wrap">
        <q-btn
          to="/stock/inventory"
          unelevated
          label="รายการส่งสินค้าทั้งหมด"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/inventory' }"
        />
        <q-btn
          to="/branch/sto"
          unelevated
          label="ร้องขอสินค้าระหว่างสาขา"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/branch/sto' }"
        />
        <q-btn
          to="/branch/sts"
          unelevated
          label="ส่งสินค้าระหว่างสาขา"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/branch/sts' }"
        />
        <q-btn
          to="/branch/br"
          unelevated
          label="รับสินค้าระหว่างสาขา"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/branch/br' }"
        />

        <!-- ปุ่มสถานะสั่งซื้อ จะไม่แสดงถ้า role ไม่ใช่ Manager -->
        <q-btn
          v-if="currentUser?.role === 'Manager'"
          to="/stock/status"
          unelevated
          label="สถานะสั่งซื้อ"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/status' }"
        />
      </div>
    </div>
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from 'src/stores/userStore'

const route = useRoute()
const isActive = ref(route.path) // ตั้งค่าเริ่มต้นให้ตรงกับ path ปัจจุบัน
const userStore = useUserStore()

// อัปเดต isActive อัตโนมัติเมื่อ route เปลี่ยน
watchEffect(() => {
  isActive.value = route.path
})

// ดึงข้อมูล currentUser จาก store
const currentUser = userStore.currentUser
</script>

<style scoped>
.bg-color {
  background-color: #f3f3f3;
}

.active-btn {
  background-color: #609fa3 !important;
  color: white;
}

/* สีของปุ่มที่ไม่ได้กด */
.q-btn {
  background-color: #b0bec5;
  color: white;
  border-radius: 10px;
  box-shadow:
    0 4px 16px 0 rgba(41, 72, 136, 0.1),
    0 1.5px 4px 0 rgba(0, 0, 0, 0.08);
}
</style>
