<template>
  <q-toolbar class="bg-color q-pt-md">
    <div class="row full-width no-wrap items-center">
      <!-- ใช้ q-space เพื่อดันปุ่มที่เหลือไปทางขวา -->
      <q-space />

      <!-- ปุ่มเมนู -->
      <div class="col-auto row no-wrap">
        <q-btn
          to="/stock/inventory"
          unelevated
          label="คลังสินค้า"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/inventory' }"
        />
        <q-btn
          to="/stock/product"
          unelevated
          label="รายการสินค้า"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/product' }"
        />
        <q-btn
          to="/stock/order"
          unelevated
          label="สั่งซื้อสินค้า"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/order' }"
        />
        <q-btn
          to="/stock/receive"
          unelevated
          label="รับสินค้า"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/receive' }"
        />
        <q-btn
          v-if="currentUser && currentUser.role === 'Manager'"
          to="/stock/status"
          unelevated
          label="สถานะสั่งซื้อ"
          class="q-mx-sm"
          :class="{ 'active-btn': isActive === '/stock/status' }"
        />
      </div>
    </div>
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from 'src/stores/authStore'
import { storeToRefs } from 'pinia'

const route = useRoute()
const isActive = ref(route.path) // ตั้งค่าเริ่มต้นให้ตรงกับ path ปัจจุบัน
const authStore = useAuthStore()

// ดึง currentUser แบบ reactive เสมอ
const { currentUser } = storeToRefs(authStore)

// ใช้ watch เพื่ออัปเดต isActive และตรวจสอบ currentUser เสมอ
watch(
  () => [route.path, currentUser.value],
  () => {
    isActive.value = route.path
  },
  { immediate: true },
)
</script>

<style scoped>
.bg-color {
  background-color: #f3f3f3;
}

.active-btn {
  background-color: #609fa3 !important;
  color: white;
}

/* สีของปุ่มที่ไม่ได้กด */
.q-btn {
  background-color: #b0bec5;
  color: white;
  border-radius: 10px;
  box-shadow:
    0 4px 16px 0 rgba(41, 72, 136, 0.1),
    0 1.5px 4px 0 rgba(0, 0, 0, 0.08);
}
</style>
