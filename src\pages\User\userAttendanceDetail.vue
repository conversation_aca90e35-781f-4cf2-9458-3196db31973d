<template>
  <UserNavigation></UserNavigation>
  <div class="q-pa-md">
    <div class="container q-pa-md">
      <div class="text-h6 q-ml-md row justify-between items-center">
        <div>ข้อมูลการเข้างาน</div>
        <q-btn color="black" flat icon="arrow_back" @click="goBack" />
      </div>

      <!-- User info cards -->
      <div class="row q-col-gutter-md q-pa-md">
        <!-- User profile card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="row items-center no-wrap">
              <q-avatar size="60px" class="q-mr-md">
                <img :src="userImage" />
              </q-avatar>
              <div>
                <div class="text-weight-bold">{{ userData?.name || 'ไม่พบข้อมูล' }}</div>
                <div class="text-caption">{{ userData?.role || 'ประจำ' }}</div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Employee ID card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="flex justify-start items-center full-height">
              <div class="row items-center">
                <q-icon name="badge" size="60px" color="primary" class="q-mr-md" />
                <div>
                  <div class="text-caption">รหัสพนักงาน</div>
                  <div class="text-weight-bold">{{ userData?.id || '00000' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Phone number card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="flex justify-start items-center full-height">
              <div class="row items-center">
                <q-icon name="phone" size="60px" color="primary" class="q-mr-md" />
                <div>
                  <div class="text-caption">เบอร์ติดต่อ</div>
                  <div class="text-weight-bold">{{ userData?.phone || '-' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Branch card -->
        <div class="col-12 col-md-3">
          <q-card flat class="user-info-card">
            <q-card-section class="flex justify-start items-center full-height">
              <div class="row items-center">
                <q-icon name="store" size="60px" color="primary" class="q-mr-md" />
                <div>
                  <div class="text-caption">สาขา</div>
                  <div class="text-weight-bold">{{ userData?.branch || 'บางแสน' }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Attendance Summary Cards -->
      <div class="q-px-md">
        <div class="row q-col-gutter-md">
          <!-- Total Days with Events Card -->
          <div class="col-12 col-md-3">
            <q-card flat class="summary-card">
              <q-card-section class="summary-card-content">
                <div class="text-subtitle2 q-mb-md">จำนวนวันลาที่เหลือ</div>
                <div class="row items-center">
                  <div class="col-5">
                    <div class="text-negative" style="width: 50px">ลากิจ</div>
                    <div class="text-h3 text-bold">{{ attendanceStats.personalLeave }}</div>
                    <div class="text-caption">วัน</div>
                  </div>
                  <q-separator vertical class="q-mx-sm" />
                  <div class="col-5">
                    <div class="text-primary" style="width: 50px">ลาป่วย</div>
                    <div class="text-h3 text-bold">{{ attendanceStats.sickLeave }}</div>
                    <div class="text-caption">วัน</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Work Performance Chart -->
          <div class="col-12 col-md-3">
            <q-card flat class="summary-card">
              <q-card-section class="summary-card-content">
                <div class="text-subtitle2 q-mb-md">การแสดงผลการทำงาน</div>
                <div class="chart-container">
                  <canvas ref="workPerformanceChart" width="120" height="120"></canvas>
                </div>
                <div class="row q-mt-sm justify-center">
                  <div class="col-6">
                    <div class="legend-item">
                      <div class="legend-color bg-positive"></div>
                      <span class="text-caption">วันทำงาน</span>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="legend-item">
                      <div class="legend-color bg-blue"></div>
                      <span class="text-caption">วันลา</span>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Attendance Performance Chart -->
          <div class="col-12 col-md-3">
            <q-card flat class="summary-card">
              <q-card-section class="summary-card-content">
                <div class="text-subtitle2 q-mb-md">กราฟแสดงเวลาการเข้างาน</div>
                <div class="chart-container">
                  <canvas ref="attendancePerformanceChart" width="120" height="120"></canvas>
                </div>
                <div class="row q-col-gutter-xs q-mt-sm">
                  <div class="col-6">
                    <div class="legend-item">
                      <div class="legend-color bg-positive"></div>
                      <span class="text-caption">ตรงเวลา</span>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="legend-item">
                      <div class="legend-color bg-orange"></div>
                      <span class="text-caption">เกินเวลา</span>
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Total Work Hours Card -->
          <div class="col-12 col-md-3">
            <q-card flat class="summary-card">
              <q-card-section class="summary-card-content">
                <div class="text-subtitle2 q-mb-md">เวลาทำงานรวม</div>
                <div class="row items-center">
                  <q-icon name="schedule" size="60px" class="q-mr-md text-orange" />
                  <div>
                    <div class="text-caption">ชั่วโมง</div>
                    <div class="text-h3 text-bold">{{ attendanceStats.totalHours }}</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>

      <!-- Attendance records table -->
      <div class="q-pa-md">
        <q-table
          :rows="attendanceRecords"
          :columns="columns"
          row-key="date"
          :pagination="{ rowsPerPage: 10 }"
          flat
          bordered
          class="attendance-detail-table"
        >
          <template v-slot:body-cell-status="props">
            <q-td :props="props">
              <q-badge
                :class="{
                  'status-badge': true,
                  'holiday-badge': props.row.status === 'วันหยุด',
                  'leave-badge': props.row.status === 'ลาป่วย' || props.row.status === 'ลากิจ',
                  'work-badge': props.row.status === 'มาทำงาน',
                  'absent-badge': props.row.status === 'ขาดงาน',
                  'late-badge': props.row.status === 'มาสาย',
                }"
              >
                {{ props.row.status }}
              </q-badge>
            </q-td>
          </template>
        </q-table>

        <!-- Loading indicator -->
        <div v-if="loading" class="text-center q-pa-md">
          <q-spinner color="primary" size="2em" />
          <div class="q-mt-sm">โหลดข้อมูล...</div>
        </div>

        <!-- Error message -->
        <div v-if="error" class="text-center q-pa-md text-negative">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import UserNavigation from 'src/components/userNavigation.vue'
import { userService } from 'src/services/userService'
import { useAttendanceStore } from 'src/stores/attendance'
import { useUserStore } from 'src/stores/userStore'
import { date } from 'quasar'
import type { AttendanceRecord } from 'src/types/attendance'
import { Chart, ArcElement, Tooltip, Legend, DoughnutController } from 'chart.js'

// Register Chart.js components
Chart.register(ArcElement, Tooltip, Legend, DoughnutController)

// Define interfaces for the data structures
interface User {
  id: number
  name?: string
  role?: string
  phone?: string
  branch?: string
  sick_leave_remaining?: number
  personal_leave_remaining?: number
  [key: string]: unknown
}

interface FormattedAttendanceRecord {
  date: string
  clock_in: string
  clock_out: string
  hours: string
  status: string
  raw_date: string
}

const route = useRoute()
const router = useRouter()
const attendanceStore = useAttendanceStore()
const userStore = useUserStore()

const userId = ref(Number(route.params.id))

const userData = ref<User | null>(null)
const userImage = ref('https://cdn.quasar.dev/img/avatar.png')
const attendanceRecords = ref<FormattedAttendanceRecord[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// Chart references
const workPerformanceChart = ref<HTMLCanvasElement>()
const attendancePerformanceChart = ref<HTMLCanvasElement>()

// Chart instances
let workPerformanceChartInstance: Chart | null = null
let attendancePerformanceChartInstance: Chart | null = null

// Computed attendance statistics
const attendanceStats = computed(() => {
  const stats = {
    totalDays: attendanceRecords.value.length,
    present: 0,
    late: 0,
    absent: 0,
    sickLeave: userData.value?.sick_leave_remaining || 0, // Remaining sick leave days
    personalLeave: userData.value?.personal_leave_remaining || 0, // Remaining personal leave days
    sickLeaveTaken: 0, // Count of sick leave days taken (for chart)
    personalLeaveTaken: 0, // Count of personal leave days taken (for chart)
    totalHours: 0,
  }

  attendanceRecords.value.forEach((record) => {
    // Count status types
    switch (record.status) {
      case 'มาทำงาน':
        stats.present++
        break
      case 'มาสาย':
        stats.late++
        break
      case 'ขาดงาน':
        stats.absent++
        break
      case 'ลาป่วย':
        stats.sickLeaveTaken++
        break
      case 'ลากิจ':
        stats.personalLeaveTaken++
        break
    }

    // Sum total hours
    if (record.hours !== '-') {
      stats.totalHours += parseFloat(record.hours)
    }
  })

  return {
    ...stats,
    totalHours: Math.round(stats.totalHours * 100) / 100, // Round to 2 decimal places
  }
})

// Table columns
const columns = [
  {
    name: 'date',
    label: 'วันที่',
    field: 'date',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'clock_in',
    label: 'เวลาเข้างาน',
    field: 'clock_in',
    align: 'center' as const,
  },
  {
    name: 'clock_out',
    label: 'เวลาออกงาน',
    field: 'clock_out',
    align: 'center' as const,
  },
  {
    name: 'hours',
    label: 'ชั่วโมงงาน',
    field: 'hours',
    align: 'center' as const,
  },
  {
    name: 'status',
    label: 'หมายเหตุ',
    field: 'status',
    align: 'center' as const,
  },
]

// Fetch user data
const fetchUserData = async () => {
  if (!userId.value) return

  try {
    // Get user details
    const user = userStore.users.find((u) => u.id === userId.value)
    if (user) {
      userData.value = {
        id: user.id,
        name: user.name,
        role: user.role,
        phone: user.tel,
        branch: user.branch?.name,
        sick_leave_remaining: user.sick_leave_remaining || 0,
        personal_leave_remaining: user.personal_leave_remaining || 0,
      }
    } else {
      // If not in store, fetch from API
      await userStore.fetchUsers()
      const fetchedUser = userStore.users.find((u) => u.id === userId.value)
      if (fetchedUser) {
        userData.value = {
          id: fetchedUser.id,
          name: fetchedUser.name,
          role: fetchedUser.role,
          phone: fetchedUser.tel,
          branch: fetchedUser.branch?.name,
          sick_leave_remaining: fetchedUser.sick_leave_remaining || 0,
          personal_leave_remaining: fetchedUser.personal_leave_remaining || 0,
        }
      }
    }

    // Get user image
    const imageUrl = await userService.getUserImageById(userId.value)
    if (imageUrl) {
      userImage.value = imageUrl
    }
  } catch (error) {
    console.error('Error fetching user data:', error)
  }
}

// Fetch attendance records
const fetchAttendanceRecords = async () => {
  if (!userId.value) return

  loading.value = true
  error.value = null

  try {
    await attendanceStore.fetchUserAttendance(userId.value)
    const records = attendanceStore.attendanceRecords

    attendanceRecords.value = records.map((record: AttendanceRecord) => {
      const formattedDate = date.formatDate(new Date(record.date), 'YYYY-MM-DD')

      let status = '-'
      if (record.status === 'Present') {
        status = 'มาทำงาน'
      } else if (record.status === 'Late') {
        status = 'มาสาย'
      } else if (record.status === 'Absent') {
        status = 'ขาดงาน'
      } else if (record.status === 'Sick Leave') {
        status = 'ลาป่วย'
      } else if (record.status === 'Personal Leave') {
        status = 'ลากิจ'
      } else if (record.status === 'Leave') {
        status = 'ลา'
      }

      // ใช้เวลา work_duration ถ้ามี
      const hours = record.work_duration != null ? record.work_duration.toFixed(2) : '-'

      return {
        date: formattedDate,
        clock_in: record.clock_in || '-',
        clock_out: record.clock_out || '-',
        hours: hours,
        status: status,
        raw_date: record.date,
      }
    })

    // Sort by date
    attendanceRecords.value.sort((a, b) => {
      return new Date(b.raw_date).getTime() - new Date(a.raw_date).getTime()
    })
  } catch (err) {
    console.error('Error fetching attendance records:', err)
    error.value = 'ไม่สามารถโหลดข้อมูลการเข้างานได้'
  } finally {
    loading.value = false
  }
}

// Create work performance chart
const createWorkPerformanceChart = async () => {
  await nextTick() // Wait for DOM to be ready

  // Destroy existing chart
  if (workPerformanceChartInstance) {
    workPerformanceChartInstance.destroy()
  }

  // Create Work Performance Chart
  if (workPerformanceChart.value) {
    const ctx = workPerformanceChart.value.getContext('2d')
    if (ctx) {
      const workingDays = attendanceStats.value.present + attendanceStats.value.late
      const absentDays =
        attendanceStats.value.absent +
        attendanceStats.value.sickLeaveTaken +
        attendanceStats.value.personalLeaveTaken

      workPerformanceChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['วันทำงาน', 'วันลา'],
          datasets: [
            {
              data: [workingDays, absentDays],
              backgroundColor: ['#439E62', '#83A7D8'],
              borderWidth: 0,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: '70%',
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: function (context: { label?: string; parsed: number }) {
                  const label = context.label || ''
                  const value = context.parsed
                  const total = workingDays + absentDays
                  const percentage = total > 0 ? Math.round((value / total) * 100) : 0
                  return `${label}: ${value} (${percentage}%)`
                },
              },
            },
          },
        },
      })
    }
  }
}

// Create attendance performance chart
const createAttendancePerformanceChart = async () => {
  await nextTick() // Wait for DOM to be ready

  // Destroy existing chart
  if (attendancePerformanceChartInstance) {
    attendancePerformanceChartInstance.destroy()
  }

  // Create Attendance Performance Chart
  if (attendancePerformanceChart.value) {
    const ctx = attendancePerformanceChart.value.getContext('2d')
    if (ctx) {
      const onTimeDays = attendanceStats.value.present
      const lateDays = attendanceStats.value.late

      attendancePerformanceChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['ตรงเวลา', 'เกินเวลา'],
          datasets: [
            {
              data: [onTimeDays, lateDays],
              backgroundColor: ['#439E62', '#ED9B53'],
              borderWidth: 0,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: '70%',
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              callbacks: {
                label: function (context: { label?: string; parsed: number }) {
                  const label = context.label || ''
                  const value = context.parsed
                  const total = onTimeDays + lateDays
                  const percentage = total > 0 ? Math.round((value / total) * 100) : 0
                  return `${label}: ${value} วัน (${percentage}%)`
                },
              },
            },
          },
        },
      })
    }
  }
}

// Go back to attendance table
const goBack = () => {
  router.go(-1)
}

onMounted(async () => {
  await fetchUserData()
  await fetchAttendanceRecords()
  await createWorkPerformanceChart()
  await createAttendancePerformanceChart()
})
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
}

.user-info-card {
  background-color: #e1edea;
  border-radius: 10px;
  height: 100%;
}

.attendance-detail-table {
  border-radius: 8px;
  overflow: hidden;
}

.attendance-detail-table :deep(th) {
  background-color: #91d2c1 !important;
  font-weight: bold;
}

.status-badge {
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  font-weight: bold;
  display: inline-block;
}

.holiday-badge {
  background-color: #b53638;
  color: white;
}

.work-badge {
  background-color: #1f6336;
  color: white;
}

.absent-badge {
  background-color: #439e62;
  color: white;
}

.leave-badge {
  background-color: #b53638;
  color: white;
}

.late-badge {
  background-color: #ed9b53;
  color: white;
}

/* Summary Cards Styles */
.summary-card {
  background-color: #e1edea;
  border-radius: 10px;
  height: 100%;
  min-height: 180px;
  display: flex;
  align-items: center;
}

.summary-card-content {
  width: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 140px;
}

.card-divider {
  height: 60px;
  margin: 0 8px;
  opacity: 0.3;
}

.chart-container {
  position: relative;
  height: 120px;
  width: 120px;
  margin: 0 auto;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: center;
  width: 80px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}
</style>
