<template>
  <UserNavigation></UserNavigation>
  <div class="container q-pa-md q-ma-md">
    <div class="row wrap-container">
      <div class="col-8 col-md-12 col-sm-12 col-xs-12 search-container">
        <SearchComponent v-model="searchQuery" placeholder="ค้นหา" />
      </div>
      <div class="col-4 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
        <q-btn flat @click="openDialog" class="add-button q-mb-md" label="เพิ่มพนักงานใหม่" />
      </div>
    </div>

    <!-- User Cards Grid -->
    <div class="row q-col-gutter-md">
      <div v-for="user in filteredUsers" :key="user.id" class="col-12 col-sm-6 col-md-4 col-lg-3">
        <q-card class="user-card" @click="navigateToUserDetail(user.id)">
          <div class="user-id">{{ user.id }}</div>
          <div class="user-image-container">
            <q-avatar size="100px">
              <img
                :src="userImages[user.id] || 'https://cdn.quasar.dev/img/avatar.png'"
                :alt="`${user.name} avatar`"
                @error="handleImageError($event, user.id)"
                @load="handleImageLoad(user.id)"
              />
            </q-avatar>
          </div>
          <div class="user-info">
            <div class="user-name">{{ user.name }}</div>
            <div class="user-detail">
              <q-icon name="phone" size="18px" class="q-mr-xs" />
              {{ user.tel || '0989898776' }}
            </div>
            <div class="user-detail">
              <q-icon name="work" size="18px" class="q-mr-xs" />
              {{ user.role || 'ประจำ' }}
            </div>
          </div>
        </q-card>
      </div>
    </div>
  </div>
  <addUserDialog></addUserDialog>
</template>

<script setup lang="ts">
import UserNavigation from 'src/components/userNavigation.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import addUserDialog from 'src/components/dialog/addUserDialog.vue'
import { ref, onMounted, computed, watch } from 'vue'
import { useUserStore } from 'src/stores/userStore'
import { userService } from 'src/services/userService'
import { useRouter } from 'vue-router'
import { useUserDialogStore } from 'src/stores/dialog-user'

const router = useRouter()
const dialogStore = useUserDialogStore()

// Define reactive variables
const searchQuery = ref('')
const selectedFilter = ref<string>('')
const userStore = useUserStore()
const userImages = ref<Record<number, string>>({})

// Define filter options
const filterOptions = ref([
  { label: 'รหัสพนักงาน', value: 'user_id' },
  { label: 'ชื่อ-นามสกุล', value: 'fullname' },
])

// Load users when component mounts
onMounted(async () => {
  await userStore.fetchUsers()
  await loadUserImages()
})

// Watch for changes in the user list and reload images
watch(
  () => userStore.users.length,
  async (newLength, oldLength) => {
    if (newLength !== oldLength) {
      console.log('User list changed, reloading images')
      await loadUserImages()
    }
  },
)

// Load user images with enhanced error handling
const loadUserImages = async () => {
  if (userStore.users) {
    for (const user of userStore.users) {
      if (user.id) {
        try {
          const imageUrl = await userService.getUserImageById(user.id)
          if (imageUrl !== null) {
            userImages.value[user.id] = imageUrl
            console.log(`✅ Successfully loaded image for user ${user.id}: ${imageUrl}`)
          } else {
            console.log(`⚠️ No image found for user ${user.id}`)
          }
        } catch (error) {
          console.error(`❌ Failed to load image for user ${user.id}:`, error)
          // Don't set a fallback here, let the img @error handler deal with it
        }
      }
    }
  }
}

// Handle image loading errors
const handleImageError = (event: Event, userId: number) => {
  console.error(`❌ Image failed to load for user ${userId}`)
  const imgElement = event.target as HTMLImageElement
  if (imgElement) {
    // Set fallback to default avatar
    imgElement.src = 'https://cdn.quasar.dev/img/avatar.png'
    // Remove from userImages to prevent retry
    delete userImages.value[userId]
  }
}

// Handle successful image loading
const handleImageLoad = (userId: number) => {
  console.log(`✅ Image successfully displayed for user ${userId}`)
}

// Filter users based on search query
const filteredUsers = computed(() => {
  if (!userStore.users) return []

  return userStore.users.filter((user) => {
    if (!searchQuery.value) return true

    const searchLower = searchQuery.value.toLowerCase()

    if (selectedFilter.value === 'user_id') {
      return user.id.toString().includes(searchLower)
    } else {
      return user.name.toLowerCase().includes(searchLower)
    }
  })
})

// Define dialog function
const openDialog = () => {
  dialogStore.open('add')
  console.log('Opening dialog')
}

// Navigation function
const navigateToUserDetail = async (userId: number) => {
  await router.push({
    name: 'user-detail',
    params: { id: userId },
  })
}
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
  overflow-y: auto;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.user-card {
  background-color: #e1edea;
  border-radius: 10px;
  padding: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  box-shadow: none;
}

.user-id {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 2px 8px;
  font-size: 12px;
}

.user-image-container {
  margin-top: 20px;
  margin-bottom: 15px;
}

.user-info {
  width: 100%;
  text-align: center;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
}

.user-detail {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>
