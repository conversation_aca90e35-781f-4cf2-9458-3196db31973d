import { api } from 'src/boot/axios'
import type { AxiosResponse } from 'axios'

/**
 * Base service class providing common CRUD operations and error handling
 * All service classes should extend this base class for consistency
 */
export abstract class BaseService<T = any, CreateT = Partial<T>, UpdateT = Partial<T>> {
  protected abstract readonly path: string

  /**
   * Get all records
   */
  async getAll(): Promise<T[]> {
    try {
      const response: AxiosResponse<T[]> = await api.get(this.path)
      return response.data
    } catch (error) {
      console.error(`Error fetching all ${this.path}:`, error)
      throw this.handleError(error)
    }
  }

  /**
   * Get a single record by ID
   */
  async getById(id: number | string): Promise<T> {
    try {
      const response: AxiosResponse<T> = await api.get(`${this.path}/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching ${this.path} with id ${id}:`, error)
      throw this.handleError(error)
    }
  }

  /**
   * Create a new record
   */
  async create(data: CreateT): Promise<T> {
    try {
      const response: AxiosResponse<T> = await api.post(this.path, data)
      return response.data
    } catch (error) {
      console.error(`Error creating ${this.path}:`, error)
      throw this.handleError(error)
    }
  }

  /**
   * Update an existing record
   */
  async update(id: number | string, data: UpdateT): Promise<T> {
    try {
      const response: AxiosResponse<T> = await api.put(`${this.path}/${id}`, data)
      return response.data
    } catch (error) {
      console.error(`Error updating ${this.path} with id ${id}:`, error)
      throw this.handleError(error)
    }
  }

  /**
   * Delete a record
   */
  async delete(id: number | string): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`)
    } catch (error) {
      console.error(`Error deleting ${this.path} with id ${id}:`, error)
      throw this.handleError(error)
    }
  }

  /**
   * Filter records with search and filter parameters
   */
  async filter(params: { search?: string; filter?: string; [key: string]: any }): Promise<T[]> {
    try {
      const response: AxiosResponse<T[]> = await api.post(`${this.path}/filter`, params)
      return response.data
    } catch (error) {
      console.error(`Error filtering ${this.path}:`, error)
      throw this.handleError(error)
    }
  }

  /**
   * Handle and normalize errors
   */
  protected handleError(error: any): Error {
    if (error.response) {
      // Server responded with error status
      const message = error.response.data?.message || error.response.statusText || 'Server error'
      return new Error(`${message} (${error.response.status})`)
    } else if (error.request) {
      // Request was made but no response received
      return new Error('Network error - no response from server')
    } else {
      // Something else happened
      return error instanceof Error ? error : new Error(String(error))
    }
  }

  /**
   * Custom endpoint call for specialized operations
   */
  protected async customCall<R = any>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    endpoint: string,
    data?: any
  ): Promise<R> {
    try {
      const response: AxiosResponse<R> = await api.request({
        method: method.toLowerCase() as any,
        url: endpoint,
        data,
      })
      return response.data
    } catch (error) {
      console.error(`Error in custom call ${method} ${endpoint}:`, error)
      throw this.handleError(error)
    }
  }
}

/**
 * Interface for services that support pagination
 */
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

/**
 * Interface for common filter parameters
 */
export interface FilterParams {
  search?: string
  filter?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}
