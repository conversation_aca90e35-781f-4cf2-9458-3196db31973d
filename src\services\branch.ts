import { BaseService } from './baseService'
import type { Branch } from 'src/types/branch'

/**
 * Branch service class extending BaseService for consistent API patterns
 */
export class BranchService extends BaseService<Branch> {
  protected readonly path = 'branch'

  /**
   * Get all branches (override for logging)
   */
  override async getAll(): Promise<Branch[]> {
    console.log('Loading branches from service...')
    const branches = await super.getAll()
    console.log('Branches loaded:', branches)
    return branches
  }

  /**
   * Get branch by ID (alias for backward compatibility)
   */
  async getOne(id: number): Promise<Branch> {
    return this.getById(id)
  }
}

// Export service instance for backward compatibility
export const branchService = new BranchService()
