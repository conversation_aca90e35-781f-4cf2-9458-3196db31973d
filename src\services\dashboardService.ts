import { BaseService } from './baseService'

/**
 * Dashboard service class extending BaseService for consistent API patterns
 */
class DashboardService extends BaseService {
  protected readonly path = 'dashboard'

  // Inventory APIs
  async getStockByBranch(): Promise<any> {
    return this.customCall('GET', '/dashboard/inventory/by-branch')
  }

  async getTopRemainingProducts(limit = 10, order = 'desc'): Promise<any> {
    return this.customCall(
      'GET',
      `/dashboard/inventory/top-remaining-products?limit=${limit}&order=${order}`,
    )
  }

  async getValueByGroup(): Promise<any> {
    return this.customCall('GET', '/dashboard/inventory/value-by-group')
  }

  // Goods Receipt APIs
  async getGoodsReceiptVolumeByMonth(): Promise<any> {
    return this.customCall('GET', '/dashboard/goods-receipt/volume-by-month')
  }

  async getTopSuppliers(): Promise<any> {
    return this.customCall('GET', '/dashboard/goods-receipt/top-suppliers')
  }

  async getGoodsReceiptValueByMonth(): Promise<any> {
    return this.customCall('GET', '/dashboard/goods-receipt/value-by-month')
  }

  // Transfer APIs
  async getTransferVolumeByBranch(): Promise<any> {
    return this.customCall('GET', '/dashboard/transfer/volume-by-branch')
  }

  async getTopIssuedProducts(): Promise<any> {
    return this.customCall('GET', '/dashboard/transfer/top-issued-products')
  }

  async getTransferFlowMap(): Promise<any> {
    return this.customCall('GET', '/dashboard/transfer/flow-map')
  }

  // Purchase Order APIs
  async getPurchaseOrderSummaryByMonth(): Promise<any> {
    return this.customCall('GET', '/dashboard/purchase-orders/summary-by-month')
  }

  async getTopPurchaseProducts(limit = 5): Promise<any> {
    return this.customCall('GET', `/dashboard/purchase-orders/top-products?limit=${limit}`)
  }

  async getSupplierRatio(): Promise<any> {
    return this.customCall('GET', '/dashboard/purchase-orders/supplier-ratio')
  }

  // Performance APIs
  async getTopOnTimeSuppliers(): Promise<any> {
    return this.customCall('GET', '/dashboard/performance/top-on-time-suppliers')
  }

  async getSlowProducts(): Promise<any> {
    return this.customCall('GET', '/dashboard/performance/slow-products')
  }

  async getSupplierEfficiency(): Promise<any> {
    return this.customCall('GET', '/dashboard/performance/supplier-efficiency')
  }
}

// Export service instance for backward compatibility
export const dashboardService = new DashboardService()
