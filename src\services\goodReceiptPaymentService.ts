import { BaseService } from './baseService'
import type { PaymentGoodsReceipt } from 'src/types/paymentGoodsReceipt'

/**
 * Goods Receipt Payment service class extending BaseService for consistent API patterns
 */
class GoodsReceiptPaymentService extends BaseService<PaymentGoodsReceipt> {
  protected readonly path = 'payment-gr'

  /**
   * Get all goods receipt payments (override for logging)
   */
  override async getAll(): Promise<PaymentGoodsReceipt[]> {
    console.log('Loading goods receipt payments...')
    return super.getAll()
  }

  /**
   * Get payments by Goods Receipt ID
   */
  async getByGRId(id: number): Promise<PaymentGoodsReceipt[]> {
    return this.customCall('GET', `${this.path}/gr/${id}`)
  }

  /**
   * Create payment for a specific Goods Receipt
   */
  async createPayment(grId: number, payment: PaymentGoodsReceipt): Promise<PaymentGoodsReceipt> {
    return this.customCall('POST', `${this.path}/${grId}`, payment)
  }
}

// Export service instance for backward compatibility
export const goodsReceiptPaymentService = new GoodsReceiptPaymentService()
