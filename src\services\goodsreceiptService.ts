import { BaseService } from './baseService'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
import type { Stock } from 'src/types/stock'

/**
 * Goods Receipt service class extending BaseService for consistent API patterns
 */
class GoodsReceiptService extends BaseService<GoodsReceipt> {
  protected readonly path = 'gr'

  /**
   * Get all goods receipts (override for logging)
   */
  override async getAll(): Promise<GoodsReceipt[]> {
    console.log('Loading goods receipts...')
    return super.getAll()
  }

  /**
   * Get goods receipts by status
   */
  async getByStatus(status: string): Promise<GoodsReceipt[]> {
    return this.customCall('GET', `${this.path}/status/${status}`)
  }

  /**
   * Get products by distributor
   */
  async getProductByDistrbutor(
    branchId: number,
    id: number,
    search = '',
    filter = '',
  ): Promise<Stock[]> {
    return this.customCall('POST', `${this.path}/branch/${branchId}/distributor/${id}`, {
      search,
      filter,
    })
  }

  /**
   * Delete PO items
   */
  async deletePoItems(selectedIds: number[]): Promise<void> {
    console.log('Deleting PO items:', selectedIds)
    await this.customCall('DELETE', `gr_detail/${selectedIds.join(',')}`, { ids: selectedIds })
  }

  /**
   * Create Goods Receipt from Distributor
   */
  async createFromDistributor(gr: GoodsReceipt): Promise<GoodsReceipt> {
    const payload = {
      ...gr,
      date_document: gr.date_document ? gr.date_document.toISOString() : new Date().toISOString(),
      tax_invoice_date: gr.tax_invoice_date ? gr.tax_invoice_date.toISOString() : null,
      credit_date: gr.credit_date ? gr.credit_date.toISOString() : null,
    }
    return this.customCall('POST', this.path, payload)
  }

  /**
   * Create Goods Receipt from Purchase Order
   */
  async createFromPO(POId: number, gr: GoodsReceipt): Promise<GoodsReceipt> {
    const payload = {
      ...gr,
      date_document: gr.date_document ? gr.date_document.toISOString() : new Date().toISOString(),
      tax_invoice_date: gr.tax_invoice_date ? gr.tax_invoice_date.toISOString() : null,
      credit_date: gr.credit_date ? gr.credit_date.toISOString() : null,
    }
    return this.customCall('POST', `${this.path}/createByPO/${POId}`, payload)
  }

  /**
   * Filter goods receipts (custom method with specific parameters)
   */
  async filterGoodsReceipts(
    search: string,
    filter: string,
    startDate: string,
    endDate: string,
  ): Promise<GoodsReceipt[]> {
    try {
      return this.customCall('POST', `${this.path}/filter`, {
        search,
        filter,
        startDate,
        endDate,
      })
    } catch (error) {
      console.error('Error filtering goods receipts', error)
      throw error
    }
  }
}

// Export service instance for backward compatibility
export const goodsReceiptService = new GoodsReceiptService()
