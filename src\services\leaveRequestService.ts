import { BaseService } from './baseService'
import type { LeaveRequest } from 'src/types/leaveRequest'

/**
 * Leave Request service class extending BaseService for consistent API patterns
 */
export class LeaveRequestService extends BaseService<LeaveRequest> {
  protected readonly path = 'leave-request'

  /**
   * Create a new leave request (custom method with specific parameters)
   */
  async createLeaveRequest(
    userId: number,
    leaveDate: string,
    leaveType: string,
    reason?: string,
  ): Promise<LeaveRequest> {
    return this.customCall('POST', `${this.path}/create`, {
      userId,
      leaveDate,
      leaveType,
      reason,
    })
  }

  /**
   * Get leave requests for a specific user
   */
  async getUserLeaveRequests(userId: number): Promise<LeaveRequest[]> {
    return this.customCall('GET', `${this.path}/user/${userId}`)
  }

  /**
   * Get all leave requests (admin only)
   */
  async getAllLeaveRequests(): Promise<LeaveRequest[]> {
    return this.customCall('GET', `${this.path}/all`)
  }

  /**
   * Update user's day off
   */
  async updateDayOff(userId: number, dayOff: string): Promise<any> {
    return this.customCall('POST', `${this.path}/update-day-off`, {
      userId,
      dayOff,
    })
  }
}

// Export service instance for backward compatibility
export const leaveRequestService = new LeaveRequestService()
