import { BaseService } from './baseService'
import type { Product, ProductExpirationAlert } from 'src/types/product'
import type { ProductGroup } from 'src/types/product-group'
import type { SpecialReportGroup } from 'src/types/special-report-group'

/**
 * Product service class extending BaseService for consistent API patterns
 */
class ProductService extends BaseService<Product> {
  protected readonly path = 'product'

  /**
   * Get all products (alias for getAll for backward compatibility)
   */
  async getProducts(): Promise<Product[]> {
    console.log('Loading products...')
    return this.getAll()
  }

  /**
   * Delete product (alias for delete for backward compatibility)
   */
  async deleteProduct(id: number): Promise<void> {
    return this.delete(id)
  }

  /**
   * Add product (alias for create for backward compatibility)
   */
  async addProduct(product: Product): Promise<Product> {
    return this.create(product)
  }

  /**
   * Update product (alias for update for backward compatibility)
   */
  async updateProduct(product: Product): Promise<Product> {
    return this.update(product.id, product)
  }

  /**
   * Filter products with search and filter parameters
   */
  async filterProducts(search: string, filter?: string): Promise<Product[]> {
    return this.filter({ search, filter })
  }

  /**
   * Break product into smaller units
   */
  async breakProduct(
    fromProductId: number,
    toProductId: number,
    quantity: number,
    branchId: number,
  ): Promise<any> {
    return this.customCall('POST', `${this.path}/${fromProductId}/break`, {
      toId: toProductId,
      quantity,
      branchId,
    })
  }

  /**
   * Get product expiration alerts
   */
  async getExpirationAlerts(): Promise<ProductExpirationAlert[]> {
    return this.customCall('GET', `${this.path}/expiration-alerts`)
  }
}

/**
 * Product Group service class
 */
class ProductGroupService extends BaseService<ProductGroup> {
  protected readonly path = 'productgroup'

  /**
   * Get all product groups (alias for backward compatibility)
   */
  async getProductGroups(): Promise<ProductGroup[]> {
    console.log('Loading product groups...')
    return this.getAll()
  }
}

/**
 * Special Report Group service class
 */
class SpecialReportGroupService extends BaseService<SpecialReportGroup> {
  protected readonly path = 'specialreportgroup'

  /**
   * Get all special report groups (alias for backward compatibility)
   */
  async getSpecialReportGroup(): Promise<SpecialReportGroup[]> {
    console.log('Loading special report groups...')
    return this.getAll()
  }
}

// Export service instances for backward compatibility
export const productService = new ProductService()
export const productGroupService = new ProductGroupService()
export const specialReportGroupService = new SpecialReportGroupService()

// Export classes for advanced usage
export { ProductService, ProductGroupService, SpecialReportGroupService }
