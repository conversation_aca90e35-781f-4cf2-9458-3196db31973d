import { BaseService } from './baseService'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import type { PurchaseOrderItems } from 'src/types/purchaseOrderitems'

/**
 * Purchase Order service class extending BaseService for consistent API patterns
 */
class PurchaseOrderService extends BaseService<PurchaseOrder> {
  protected readonly path = 'purchaseorder'

  /**
   * Get all purchase orders (override for logging)
   */
  override async getAll(): Promise<PurchaseOrder[]> {
    console.log('Loading purchase orders...')
    return super.getAll()
  }

  /**
   * Get purchase order items by PO ID
   */
  async getPOItemByPOId(id: number): Promise<PurchaseOrderItems[]> {
    return this.customCall('GET', `${this.path}/${id}/purchaseorderitem`)
  }

  /**
   * Delete purchase order items
   */
  async deletePoItems(selectedIds: number[]): Promise<void> {
    console.log('Deleting PO items:', selectedIds)
    await this.customCall('DELETE', `purchaseorderitem/${selectedIds.join(',')}`, {
      ids: selectedIds,
    })
  }

  /**
   * Create purchase order with date formatting
   */
  override async create(order: Partial<PurchaseOrder>): Promise<PurchaseOrder> {
    const payload = {
      ...order,
      order_date: order.order_date ? order.order_date.toISOString() : new Date().toISOString(),
    }
    return super.create(payload)
  }

  /**
   * Filter purchase orders (custom method with specific parameters)
   */
  async filterPurchaseOrders(
    search: string,
    filter: string,
    startDate: string,
    endDate: string,
  ): Promise<PurchaseOrder[]> {
    try {
      return this.customCall('POST', `${this.path}/filter`, {
        search,
        filter,
        startDate,
        endDate,
      })
    } catch (error) {
      console.error('Error filtering purchase orders', error)
      throw error
    }
  }
}

// Export service instance for backward compatibility
export const purchaseOrderService = new PurchaseOrderService()
