import { BaseService } from './baseService'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'

/**
 * Stock Transfer Order service class extending BaseService for consistent API patterns
 */
export class StockTransferOrderService extends BaseService<StockTransferOrder> {
  protected readonly path = 'sto'

  /**
   * Get all STOs (alias for backward compatibility)
   */
  async getAll(): Promise<StockTransferOrder[]> {
    console.log('Loading stock transfer orders...')
    return super.getAll()
  }

  /**
   * Get STO by ID (alias for backward compatibility)
   */
  async getOne(id: number): Promise<StockTransferOrder> {
    return this.getById(id)
  }

  /**
   * Get STO by ID (alias for backward compatibility)
   */
  async getSTOById(id: number): Promise<StockTransferOrder> {
    return this.getById(id)
  }

  /**
   * Get products by branch
   */
  async getProductByBranch(id: number): Promise<any> {
    return this.customCall('GET', `${this.path}/product/${id}`)
  }

  /**
   * Get STOs by status
   */
  async getSTOByStatus(status: string): Promise<StockTransferOrder[]> {
    return this.customCall('GET', `${this.path}/status/${status}`)
  }

  /**
   * Create STO with date formatting
   */
  async create(sto: Partial<StockTransferOrder>): Promise<StockTransferOrder> {
    const payload = {
      ...sto,
      request_date: sto.request_date ? sto.request_date.toISOString() : new Date().toISOString(),
    }
    return super.create(payload)
  }

  /**
   * Update STO (alias for backward compatibility)
   */
  async updateOne(id: number, obj: Partial<StockTransferOrder>): Promise<StockTransferOrder> {
    return this.update(id, obj)
  }
}

// Export service instance for backward compatibility
export const stockTransferOrderService = new StockTransferOrderService()
