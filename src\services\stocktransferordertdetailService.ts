import { BaseService } from './baseService'
import type { StockTransferOrderDetails } from 'src/types/stockTransferOrderDetails'

/**
 * Stock Transfer Order Details service class extending BaseService for consistent API patterns
 */
export class StockTransferOrderDetailsService extends BaseService<StockTransferOrderDetails> {
  protected readonly path = 'sto-details'

  /**
   * Get all STO details (override for logging)
   */
  override async getAll(): Promise<StockTransferOrderDetails[]> {
    console.log('Loading stock transfer order details...')
    return super.getAll()
  }

  /**
   * Get details by STO ID
   */
  async getDetailsBySTO(id: number): Promise<StockTransferOrderDetails[]> {
    return this.customCall('GET', `${this.path}/sto/${id}`)
  }

  /**
   * Create STO details for a specific STO
   */
  async create(stoDetails: StockTransferOrderDetails[], stoId: number): Promise<void> {
    await this.customCall('POST', `${this.path}/${stoId}`, stoDetails)
  }

  /**
   * Update STO details from PO
   */
  async updateGRDetailsFromPO(grDetails: StockTransferOrderDetails[], grId: number): Promise<void> {
    await this.customCall('PUT', `${this.path}/${grId}`, grDetails)
  }
}

// Export service instance for backward compatibility
export const stockTransferOrderDetailsService = new StockTransferOrderDetailsService()
