import { BaseService } from './baseService'
import type { Stock } from 'src/types/stock'
import type { StockTransferSlip } from 'src/types/stockTransferSlip'

/**
 * Stock Transfer Slip service class extending BaseService for consistent API patterns
 */
class StockTransferSlipService extends BaseService<StockTransferSlip> {
  protected readonly path = 'sts'

  /**
   * Get all stock transfer slips (override for logging)
   */
  override async getAll(): Promise<StockTransferSlip[]> {
    console.log('Loading stock transfer slips...')
    return super.getAll()
  }

  /**
   * Get stock transfer slips by status
   */
  async getSTSByStatus(status: string): Promise<StockTransferSlip[]> {
    return this.customCall('GET', `${this.path}/status/${status}`)
  }

  /**
   * Get products by branch
   */
  async getProductByBranch(productId: number): Promise<Stock[]> {
    return this.customCall('GET', `${this.path}/product/${productId}`)
  }

  /**
   * Create stock transfer slip with date formatting
   */
  override async create(sts: Partial<StockTransferSlip>): Promise<StockTransferSlip> {
    const payload = {
      ...sts,
      transfer_date: sts.transfer_date ? sts.transfer_date.toISOString() : new Date().toISOString(),
    }
    return super.create(payload)
  }

  /**
   * Create stock transfer slip from STO
   */
  async createFromSTO(stoId: number): Promise<StockTransferSlip> {
    return this.customCall('POST', `${this.path}/createByPO/${stoId}`)
  }

  /**
   * Update STS created by STO
   */
  async updateSTSCreateBySTO(
    id: number,
    sts: Partial<StockTransferSlip>,
  ): Promise<StockTransferSlip> {
    return this.update(id, sts)
  }
}

// Export service instance for backward compatibility
export const stockTransferSlipService = new StockTransferSlipService()

// Export with the original typo name for backward compatibility
export const stockTransFerSlipService = stockTransferSlipService
