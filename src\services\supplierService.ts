import { BaseService } from './baseService'
import type { Supplier } from 'src/types/supplier'

/**
 * Supplier service class extending BaseService for consistent API patterns
 */
class SupplierService extends BaseService<Supplier> {
  protected readonly path = 'supplier'

  /**
   * Get all suppliers (alias for backward compatibility)
   */
  async getSuppliers(): Promise<Supplier[]> {
    console.log('Loading suppliers...')
    return this.getAll()
  }

  /**
   * Delete supplier (alias for backward compatibility)
   */
  async deleteSupplier(id: number): Promise<void> {
    return this.delete(id)
  }

  /**
   * Add supplier (alias for backward compatibility)
   */
  async addSupplier(supplier: Supplier): Promise<Supplier> {
    return this.create(supplier)
  }

  /**
   * Update supplier (alias for backward compatibility)
   */
  async updateSupplier(supplier: Supplier): Promise<Supplier> {
    return this.update(supplier.id, supplier)
  }

  /**
   * Filter suppliers with search, filter, and type parameters
   */
  async getSuppliersByFilter(search: string, filter: string, type: string): Promise<Supplier[]> {
    console.log('Filtering suppliers...')
    return this.filter({ search, filter, type })
  }
}

// Export service instance for backward compatibility
export const supplierService = new SupplierService()

// Export class for advanced usage
export { SupplierService }
