import { BaseService } from './baseService'
import type { user } from 'src/types/user'

/**
 * User service class extending BaseService with additional user-specific methods
 */
export class UserService extends BaseService<user> {
  protected readonly path = 'user'

  /**
   * Update user's day off schedule
   */
  async updateDayOff(userId: number, dayOff: string): Promise<user> {
    try {
      console.log('Updating day_off for user:', userId)
      return this.update(userId, { day_off: dayOff } as Partial<user>)
    } catch (error) {
      console.error('Failed to update day_off:', error)
      throw error
    }
  }

  /**
   * User login
   */
  async login(name: string, password: string): Promise<any> {
    try {
      const res = await this.customCall('POST', 'auth/login', { name, password })
      console.log('User logged in:', res)

      // Log user image path if available
      if (res && res.id) {
        try {
          // Get user image using the dedicated endpoint
          const imageUrl = await this.getUserImageById(res.id)
          console.log('User image URL from API:', imageUrl)

          // Add the image URL to the user data if found
          if (imageUrl) {
            res.processedImageUrl = imageUrl
          }
        } catch (error) {
          console.error('Error fetching user image:', error)
        }
      }

      return res
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  /**
   * Get all users (override base method for logging)
   */
  override async getAll(): Promise<user[]> {
    console.log('Fetching all users...')
    return super.getAll()
  }

  /**
   * Get user information by ID
   */
  async getUserInfo(userId: number): Promise<user> {
    try {
      console.log('Fetching user info for ID:', userId)
      return this.getById(userId)
    } catch (error) {
      console.error('Failed to fetch user data:', error)
      throw error
    }
  }

  /**
   * Get user image URL with consistent handling
   */
  getUserImageUrl(imagePath: string): string | null {
    // If no image path is provided, return null
    if (!imagePath) {
      console.log('No image path provided')
      return null
    }

    // Log the incoming image path
    console.log('Original image path:', imagePath)

    // If the path is already a full URL, return it
    if (imagePath.startsWith('http')) {
      const fullUrl = imagePath
      console.log('Using full URL:', fullUrl)
      return fullUrl
    }

    // Special case for default image
    if (imagePath === 'noimage.png') {
      return null // Return null to trigger the fallback icon
    }

    // Get base URL without trailing slash - ensure consistent base URL
    const baseURL = 'http://localhost:3000' // Default base URL

    // Normalize the image path - remove leading slash if present
    const normalizedPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath

    // Construct the full URL consistently
    const fullImageUrl = `${baseURL}/${normalizedPath}`
    console.log('Constructed image URL:', fullImageUrl)

    return fullImageUrl
  }

  /**
   * Filter users with search, filter, and role parameters
   */
  async filterUsers(search: string = '', filter: string = '', role: string = ''): Promise<user[]> {
    try {
      console.log('Filtering users...')
      return this.filter({ search, filter, role })
    } catch (error) {
      console.error('Failed to filter users:', error)
      throw error
    }
  }

  /**
   * Get user image using the dedicated API endpoint with enhanced error handling
   */
  async getUserImageById(userId: number): Promise<string | null> {
    try {
      const res = await this.customCall('GET', `${this.path}/image/${userId}`)
      console.log('User image data:', res)

      if (res && res.imagePath) {
        // Process the image path from the API response
        const imageUrl = this.getUserImageUrl(res.imagePath)

        // Validate that the constructed URL is accessible
        if (imageUrl) {
          try {
            // Test if the image URL is accessible
            const testResponse = await fetch(imageUrl, { method: 'HEAD' })
            if (testResponse.ok) {
              console.log(`✅ Image URL validated for user ${userId}: ${imageUrl}`)
              return imageUrl
            } else {
              console.warn(
                `⚠️ Image URL not accessible for user ${userId}: ${imageUrl} (Status: ${testResponse.status})`,
              )
              return null
            }
          } catch (fetchError) {
            console.warn(
              `⚠️ Failed to validate image URL for user ${userId}: ${imageUrl}`,
              fetchError,
            )
            // Return the URL anyway, let the img element handle the error
            return imageUrl
          }
        }
      }
      return null
    } catch (error) {
      console.error(`❌ Failed to fetch user image for user ${userId}:`, error)
      return null
    }
  }

  /**
   * Add new user (alias for create)
   */
  async addUser(userData: Partial<user>): Promise<user> {
    try {
      console.log('Adding user:', userData)
      return this.create(userData)
    } catch (error) {
      console.error('Failed to add user:', error)
      throw error
    }
  }

  /**
   * Update existing user (alias for update)
   */
  async updateUser(userData: Partial<user>): Promise<user> {
    try {
      if (!userData.id) {
        throw new Error('User ID is required for update')
      }
      console.log('Updating user:', userData)
      return this.update(userData.id, userData)
    } catch (error) {
      console.error('Failed to update user:', error)
      throw error
    }
  }

  /**
   * Delete user (alias for delete)
   */
  async deleteUser(userId: number): Promise<void> {
    try {
      console.log('Deleting user:', userId)
      await this.delete(userId)
    } catch (error) {
      console.error('Failed to delete user:', error)
      throw error
    }
  }

  /**
   * Upload user image
   */
  async uploadUserImage(userId: number, imageFile: File): Promise<any> {
    try {
      const formData = new FormData()
      formData.append('image', imageFile)
      formData.append('userId', userId.toString())

      // Try the primary upload endpoint
      const res = await this.customCall('POST', `${this.path}/upload-image`, formData)
      console.log('Image uploaded:', res)
      return res
    } catch (error: unknown) {
      console.error('Failed to upload image:', error)

      // If it's a 404 error, the endpoint doesn't exist
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as { response?: { status?: number } }
        if (axiosError.response?.status === 404) {
          console.warn('Image upload endpoint not available. Skipping image upload.')
          return { success: false, message: 'Image upload endpoint not available' }
        }
      }

      throw error
    }
  }

  /**
   * Add user with image data included
   */
  async addUserWithImage(userData: Partial<user>, imageFile?: File): Promise<user> {
    try {
      const userDataWithImage = { ...userData }

      // If image file is provided, convert to base64 and include in user data
      if (imageFile) {
        try {
          const base64Image = await this.convertFileToBase64(imageFile)
          userDataWithImage.image = base64Image
        } catch (imageError) {
          console.warn('Failed to process image, creating user without image:', imageError)
        }
      }

      console.log('Adding user with image:', userDataWithImage)
      return this.create(userDataWithImage)
    } catch (error) {
      console.error('Failed to add user with image:', error)
      throw error
    }
  }

  /**
   * Helper method to convert file to base64
   */
  private convertFileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result)
        } else {
          reject(new Error('Failed to convert file to base64'))
        }
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
      reader.readAsDataURL(file)
    })
  }
}

// Export service instance for backward compatibility
export const userService = new UserService()
