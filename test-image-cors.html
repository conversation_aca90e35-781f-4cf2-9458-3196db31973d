<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .image-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .image-test img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 50%;
            margin-right: 15px;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #fff3cd; color: #856404; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Image CORS Test</h1>
    <p>Testing image loading from the backend server to identify CORS issues.</p>
    
    <div class="test-container">
        <h2>Direct Image URLs</h2>
        <div id="direct-tests"></div>
    </div>
    
    <div class="test-container">
        <h2>API Endpoint Tests</h2>
        <div id="api-tests"></div>
    </div>
    
    <div class="test-container">
        <h2>Console Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        const log = document.getElementById('log');
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'warn' ? '#ffc107' : '#28a745';
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        const baseURL = 'http://localhost:3000';
        const testUsers = [1, 3, 5]; // Test different user IDs
        
        function createImageTest(url, description, container) {
            const testDiv = document.createElement('div');
            testDiv.className = 'image-test';
            
            const img = document.createElement('img');
            const status = document.createElement('span');
            const urlSpan = document.createElement('span');
            
            status.className = 'status loading';
            status.textContent = 'Loading...';
            
            urlSpan.textContent = `${description}: ${url}`;
            
            img.crossOrigin = 'anonymous';
            img.src = url;
            
            img.onload = function() {
                status.className = 'status success';
                status.textContent = 'Success';
                console.log(`✅ Image loaded successfully: ${url}`);
            };
            
            img.onerror = function() {
                status.className = 'status error';
                status.textContent = 'Failed';
                console.error(`❌ Image failed to load: ${url}`);
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiNkZGQiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMyIgeT0iMTMiPgo8cGF0aCBkPSJNMTIgMTJDMTQuMjA5MSAxMiAxNiAxMC4yMDkxIDE2IDhDMTYgNS43OTA5IDE0LjIwOTEgNCAxMiA0QzkuNzkwODYgNCA4IDUuNzkwOSA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOTk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcgMTYuMzYgNyAxOVYyMEgxN1YxOUMxNyAxNi4zNiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5OTkiLz4KPC9zdmc+Cjwvc3ZnPgo=';
            };
            
            testDiv.appendChild(img);
            testDiv.appendChild(urlSpan);
            testDiv.appendChild(status);
            container.appendChild(testDiv);
        }
        
        async function testAPIEndpoint(userId, container) {
            try {
                const response = await fetch(`${baseURL}/user/image/${userId}`);
                const data = await response.json();
                
                if (data.imagePath) {
                    const fullURL = data.imagePath.startsWith('http') 
                        ? data.imagePath 
                        : `${baseURL}${data.imagePath.startsWith('/') ? '' : '/'}${data.imagePath}`;
                    
                    createImageTest(fullURL, `User ${userId} API`, container);
                    console.log(`API response for user ${userId}:`, data);
                } else {
                    console.warn(`No image path for user ${userId}`);
                }
            } catch (error) {
                console.error(`Failed to fetch API data for user ${userId}:`, error);
            }
        }
        
        // Run tests
        document.addEventListener('DOMContentLoaded', function() {
            const directContainer = document.getElementById('direct-tests');
            const apiContainer = document.getElementById('api-tests');
            
            console.log('Starting image CORS tests...');
            
            // Test direct image URLs
            testUsers.forEach(userId => {
                createImageTest(`${baseURL}/images/user/user${userId}.jpeg`, `User ${userId} Direct`, directContainer);
            });
            
            // Test API endpoints
            testUsers.forEach(userId => {
                testAPIEndpoint(userId, apiContainer);
            });
        });
    </script>
</body>
</html>
